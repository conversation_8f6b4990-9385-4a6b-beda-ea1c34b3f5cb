import axios, { InternalAxiosRequestConfig, AxiosResponse } from "axios";
// Removed coordinatedSessionRefresh - now using AuthContext directly

// AI ASSISTANT CHAT FIX - Standardized API Configuration

// Determine API base URL based on environment
const getApiBaseUrl = () => {
  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';

  // For development, check if backend is accessible directly
  if (isDevelopment && window.location.hostname === 'localhost') {
    // Use proxy in development
    return '/api';
  }

  // For production or Docker environments, always use relative URLs
  return '/api';
};

// Force reload by changing timestamp
const FORCE_RELOAD_TIMESTAMP = Date.now();

// Create axios instance with standardized config
const api = axios.create({
  baseURL: getApiBaseUrl(),
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
  withCredentials: false,
  timeout: 130000, // 130 seconds to handle long AI responses
});

// Log the API configuration
console.log('API Service initialized with baseURL:', api.defaults.baseURL);

// Import AuthContext utilities (avoiding circular imports)
let authContextUtils: {
  getCurrentSession: () => any;
  refreshSession: () => Promise<any>;
  signOut: () => Promise<void>;
} | null = null;

// Function to set auth context utilities (called from AuthContext)
export const setAuthContextUtils = (utils: typeof authContextUtils) => {
  authContextUtils = utils;
};

// Request interceptor that attaches the current token with validation
api.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    try {
      // Get current session from AuthContext (no direct Supabase calls)
      const session = authContextUtils?.getCurrentSession();

      // Enhanced logging for chat-related requests
      const isChatRequest = config.url?.includes('/ai_assistant/chat');
      if (isChatRequest) {
        console.log(`[CHAT API] ${config.method?.toUpperCase()} ${config.url} - Session: ${!!session}`);
      }

      if (session?.access_token) {
        // Check if token is expired (with 5 minute buffer)
        const now = Math.floor(Date.now() / 1000);
        const tokenExpiry = session.expires_at || 0;
        const bufferTime = 5 * 60; // 5 minutes in seconds

        if (tokenExpiry > 0 && (now + bufferTime) >= tokenExpiry) {
          console.log("Access token is near expiry, will be refreshed by response interceptor");
        }

        // Use the Supabase access token for authorization
        config.headers.Authorization = `Bearer ${session.access_token}`;

        // Add enhanced headers for chat requests
        const clientTime = new Date();
        config.headers["X-Client-Time"] = clientTime.toISOString();
        config.headers["X-Client-Timezone"] = Intl.DateTimeFormat().resolvedOptions().timeZone;
        config.headers["X-Time-Offset"] = clientTime.getTimezoneOffset().toString();

        if (isChatRequest) {
          config.headers["X-Chat-Request"] = "true";
          console.log(`[CHAT API] Token attached, expires at: ${new Date(tokenExpiry * 1000).toISOString()}`);
        }
      } else {
        if (isChatRequest) {
          console.error("[CHAT API] No valid session found for chat request:", config.url);
        } else {
          console.log("No valid session found for API request to:", config.url);
        }
      }

      return config;
    } catch (error) {
      console.error("Request interceptor error:", error);
      // Continue with request even if session check fails
      return config;
    }
  },
  (error) => {
    console.error("Request error:", error);
    return Promise.reject(error);
  }
);

// State for handling token refresh (now using centralized auth manager)
let isRefreshing = false;
let refreshPromise: Promise<any> | null = null;

let failedQueue: {
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
  config: InternalAxiosRequestConfig;
}[] = [];

// Function to process the failed queue
const processQueue = (error: any | null, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else if (token) {
      prom.config.headers["Authorization"] = `Bearer ${token}`;
      // Retry the request with the new token
      api(prom.config)
        .then((response) => prom.resolve(response))
        .catch((err) => prom.reject(err));
    }
  });
  failedQueue = []; // Clear the queue
};

// Removed coordinatedRefresh - now using AuthContext directly

// Helper to check if we're on auth pages
const isOnAuthPage = () => {
  return ["/login", "/register", "/reset-password", "/forgot-password"].includes(
    window.location.pathname
  );
};

// Simplified response interceptor that defers to AuthContext
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    console.error("API Error:", error.response?.data || error.message);
    console.error("Request URL:", error.config?.url);
    console.error("Request Method:", error.config?.method);

    const originalRequest = error.config as InternalAxiosRequestConfig;

    // Handle 401 Unauthorized errors - Defer to AuthContext for refresh
    if (error.response?.status === 401 && originalRequest && authContextUtils) {
      console.log("Unauthorized access - deferring to AuthContext for token refresh.");

      // Check if we're on an auth page (don't try to refresh)
      if (isOnAuthPage()) {
        console.log("On auth page, not attempting refresh");
        return Promise.reject(error);
      }

      // Prevent infinite retry loops
      const retryCount = (originalRequest as any)._retryCount || 0;
      if (retryCount >= 2) {
        console.log("Max retry attempts reached, signing out");
        await authContextUtils.signOut();
        if (!isOnAuthPage()) {
          window.location.href = "/login";
        }
        return Promise.reject(error);
      }

      (originalRequest as any)._retryCount = retryCount + 1;

      // Always queue the request first
      const retryOriginalRequest = new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject, config: originalRequest });
      });

      // If refresh is already in progress, just return the queued promise
      if (isRefreshing) {
        console.log("Token refresh in progress, request queued:", originalRequest.url);
        return retryOriginalRequest;
      }

      // If we have a refresh promise in progress, wait for it
      if (refreshPromise) {
        console.log("Waiting for existing refresh promise:", originalRequest.url);
        try {
          await refreshPromise;
          // The processQueue should have been called, so just return the promise
          return retryOriginalRequest;
        } catch (error) {
          // If the refresh failed, the error should have been processed
          return retryOriginalRequest;
        }
      }

      // Start the refresh process using AuthContext
      isRefreshing = true;
      console.log("Starting AuthContext token refresh for 401 error");

      // Create and store the refresh promise - delegate to AuthContext
      refreshPromise = (async () => {
        try {
          const newSession = await authContextUtils.refreshSession();

          if (newSession?.access_token) {
            console.log("AuthContext refresh successful");
            processQueue(null, newSession.access_token);
          } else {
            throw new Error("AuthContext refresh failed - no session");
          }
        } catch (refreshError) {
          console.error("AuthContext refresh failed:", refreshError);

          // For any refresh error, just reject the requests
          // Don't sign out immediately - let AuthContext handle that
          processQueue(refreshError, null);

          // Only sign out for non-rate-limiting errors
          if (!(refreshError instanceof Error && refreshError.message.includes('rate limited'))) {
            await authContextUtils.signOut();
            if (!isOnAuthPage()) {
              window.location.href = "/login";
            }
          }
        } finally {
          isRefreshing = false;
          refreshPromise = null;
        }
      })();

      // Wait for the refresh to complete
      await refreshPromise;
      return retryOriginalRequest;
    }

    return Promise.reject(error);
  }
);

// Helper function to check if token exists and is valid using AuthContext
export const hasValidToken = async (): Promise<boolean> => {
  try {
    // Check for valid session from AuthContext
    const session = authContextUtils?.getCurrentSession();
    
    if (!session?.access_token) {
      return false; // No session found
    }

    return true;
  } catch (error) {
    console.error("Error checking token validity:", error);
    return false;
  }
};

// Health check function
export const pingApi = async (): Promise<boolean> => {
  try {
    console.log("Performing API health check...");
    const response = await api.get("/health");
    
    if (response.status === 200) {
      console.log("API health check passed");
      return true;
    }
    
    console.warn("API health check returned non-200 status:", response.status);
    return false;
  } catch (error) {
    console.warn("General /health check failed, trying Supabase health...");
    // If general health fails, try Supabase specific (if applicable)
    // Replace with actual Supabase health check if needed
    return false;
  }
};

// Interface for the payload to save content
export interface SaveContentPayload {
  title: string;
  content: string;
  content_type: string;
  tags: string[];
}

// Function to save a new content item to the library
export const saveContentItem = async (
  payload: SaveContentPayload
): Promise<any> => {
  try {
    const response = await api.post("/content", payload);
    return response.data;
  } catch (error) {
    console.error("Error saving content item:", error);
    throw error; // Re-throw to be handled by the caller
  }
};

// New function for fetching a simplified list of business contexts for selection
export interface BusinessContextListItem {
  id: string; // UUID
  name: string;
}

export const getBusinessContextsList = async (): Promise<BusinessContextListItem[]> => {
  console.log("Calling GET /business to fetch context list");
  try {
    // Call the backend and expect direct array
    const resp = await api.get<any[]>("/business/");
    const list = Array.isArray(resp.data) ? resp.data : [];
    // Map each context to id/name pair
    return list.map((ctx: any) => ({
      id: ctx.id,
      name:
        typeof ctx.name === "string" && ctx.name.trim()
          ? ctx.name
          : `Context ${String(ctx.id).substring(0, 8)}`,
    }));
  } catch (error) {
    console.error("Error fetching business contexts list:", error);
    return [];
  }
};

// Added for Feedback functionality
export interface FeedbackPayload {
  session_id: string;
  message_id: string;
  feedback_type: "script_usability"; // Specific type for this widget
  rating: "up" | "down";
  comment?: string;
}

export async function submitFeedback(
  payload: FeedbackPayload
): Promise<AxiosResponse> {
  console.log("Submitting feedback:", payload);
  try {
    const response = await api.post("/feedback", payload);
    console.log("Feedback submission successful:", response.data);
    return response;
  } catch (error) {
    console.error("Feedback submission failed:", error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

// Wizard API Interfaces and Functions

// Updated interface for starting a new session
export interface WizardBrainstormPayload {
  business_context_id: string; // UUID of the selected business context
  content_idea: string;       // The user-provided content idea/topic
  // user_preferences?: Record<string, any>; // Keep if needed for other settings
}

// Interface for selecting a hook/intro/outline index
export interface WizardSelectionPayload {
  session_id: string;
  selected_index: number;
}

// Interface for the edit stage
export interface WizardEditPayload {
  session_id: string;
  style_preferences: string;  // Updated to match backend parameter name
  // Potentially include draft_text if needed by backend, though session should hold it
  // draft_text?: string;
}

// Generic interface for wizard step responses (adjust as needed)
export interface WizardStepResponse {
  session_id: string;
  results?: any; // e.g., array of brainstorm ideas, hooks, intros, outline, draft
  current_stage?: string;
  error?: string;
  // Add specific response fields for each step
  titles?: any[];
  hooks?: any[];
  intro_ctas?: any[];
  outlines?: any[];
  draft?: string;
  edited_script?: string;
  message?: string;
}

// POST /api/ai/wizard/brainstorm
export const wizardBrainstorm = async (
  payload: WizardBrainstormPayload
): Promise<AxiosResponse<WizardStepResponse>> => {
  console.log("Calling ai/wizard/brainstorm with payload:", payload);
  return api.post<WizardStepResponse>("ai/wizard/brainstorm", payload);
};

// POST /api/ai/wizard/hook
export const wizardSelectHook = async (
  payload: WizardSelectionPayload
): Promise<AxiosResponse<WizardStepResponse>> => {
  console.log("Calling ai/wizard/hook with payload:", payload);
  return api.post<WizardStepResponse>("ai/wizard/hook", payload);
};

// POST /api/ai/wizard/intro
export const wizardSelectIntro = async (
  payload: WizardSelectionPayload
): Promise<AxiosResponse<WizardStepResponse>> => {
  console.log("Calling ai/wizard/intro with payload:", payload);
  return api.post<WizardStepResponse>("ai/wizard/intro", payload);
};

// POST /api/ai/wizard/outline
export const wizardGenerateOutline = async (
  payload: WizardSelectionPayload // Reuses selection payload
): Promise<AxiosResponse<WizardStepResponse>> => {
  console.log("Calling ai/wizard/outline with payload:", payload);
  return api.post<WizardStepResponse>("ai/wizard/outline", payload);
};

// POST /api/ai/wizard/draft
export const wizardDraftScript = async (
  payload: WizardSelectionPayload // Reuses selection payload
): Promise<AxiosResponse<WizardStepResponse>> => {
  console.log("Calling ai/wizard/draft with payload:", payload);
  return api.post<WizardStepResponse>("ai/wizard/draft", payload);
};

// POST /api/ai/wizard/edit
export const wizardEditScript = async (
  payload: WizardEditPayload
): Promise<AxiosResponse<WizardStepResponse>> => {
  console.log("Calling ai/wizard/edit with payload:", payload);
  return api.post<WizardStepResponse>("ai/wizard/edit", payload);
};

export { api };
