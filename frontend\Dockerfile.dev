# Use Node.js base image
FROM node:16-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies using npm install to generate lock file if needed
RUN npm install

# Copy source code explicitly to avoid issues with spaces in paths
COPY public/ ./public/
COPY src/ ./src/
COPY tsconfig.json ./
COPY .env* ./

# Expose port
EXPOSE 3000

# Start development server
CMD ["npm", "start"]