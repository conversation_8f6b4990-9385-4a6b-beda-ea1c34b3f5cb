"""
BusinessContextRepository for Supabase implementation
Handles CRUD operations for business contexts
"""

import logging
from datetime import datetime, timezone
from app.repositories.supabase_repository import SupabaseRepository, SupabaseRepositoryError
from app.models.supabase_client import get_sync_supabase_client # For direct client access if needed, though _get_table is preferred

logger = logging.getLogger(__name__)


class BusinessContextRepository(SupabaseRepository):
    """Repository for managing business contexts in Supabase"""

    def __init__(self, supabase_client=None):
        """Initialize with Supabase client.

        Args:
            supabase_client: Optional Supabase client instance injected by FastAPI.
        """
        super().__init__(table_name="business_contexts", supabase_client=supabase_client)

    def get_by_user_id(self, user_id):
        """Get all business contexts for a specific user synchronously."""
        try:
            table = self._get_table()
            result = (
                table
                .select("*")
                .eq("user_id", user_id)
                .order("created_at", desc=True)
                .execute()
            )
            return result.data
        except Exception as e:
            self.handle_error(f"Error getting business contexts for user {user_id}", e)
            # Consistent error handling would be to raise, not return None, if handle_error raises
            raise # Re-raise the SupabaseRepositoryError from handle_error

    def get_by_id(self, context_id, user_id):
        """Get a specific business context by ID synchronously, ensuring it belongs to the user."""
        try:
            table = self._get_table()
            result = (
                table
                .select("*")
                .eq("id", context_id)
                .eq("user_id", user_id)
                .execute()
            )
            if result.data and len(result.data) > 0:
                return result.data[0]
            return None
        except Exception as e:
            self.handle_error(
                f"Error getting business context {context_id} for user {user_id}", e
            )
            raise # Re-raise

    def create_business_context(self, user_id, business_context_data):
        """Create a new business context synchronously."""
        try:
            required_fields = ["offer_description", "target_audience", "brand_voice"]
            for field in required_fields:
                if field in ["offer_description", "target_audience"] and not business_context_data.get(field):
                    raise ValueError(f"Missing required field: {field}")

            logger.debug(f"Creating business context for user: {user_id}")
            now = datetime.now(timezone.utc).isoformat()
            data_to_insert = {
                "user_id": user_id,
                "offer_description": business_context_data.get("offer_description"),
                "target_audience": business_context_data.get("target_audience"),
                "brand_voice": business_context_data.get("brand_voice"),
                "key_benefits": business_context_data.get("key_benefits"),
                "unique_value_proposition": business_context_data.get("unique_value_proposition"),
                "audience_motivation": business_context_data.get("audience_motivation"),
                "audience_decision_style": business_context_data.get("audience_decision_style"),
                "audience_preference_structure": business_context_data.get("audience_preference_structure"),
                "audience_decision_speed": business_context_data.get("audience_decision_speed"),
                "audience_reaction_to_change": business_context_data.get("audience_reaction_to_change"),
                "recommended_platforms": business_context_data.get("recommended_platforms"),
                "recommended_content_formats": business_context_data.get("recommended_content_formats"),
                "created_at": now,
                "updated_at": now,
            }
            # Add name if provided
            if "name" in business_context_data and business_context_data["name"]:
                data_to_insert["name"] = business_context_data["name"]
            # Add profile_overview and content_focus from new structure
            if "profile_overview" in business_context_data:
                 data_to_insert["profile_overview"] = business_context_data["profile_overview"]
            if "content_focus" in business_context_data:
                 data_to_insert["content_focus"] = business_context_data["content_focus"]
            if "primary_objectives" in business_context_data:
                data_to_insert["primary_objectives"] = business_context_data["primary_objectives"]


            table = self._get_table()
            result = table.insert(data_to_insert).execute()

            if result.data and len(result.data) > 0:
                return result.data[0]
            logger.warning(
                f"Insert operation for user {user_id} returned no data. Check RLS policies or Supabase logs."
            )
            # If insert fails to return data, it's an issue. Raise an error.
            raise SupabaseRepositoryError("Failed to create business context: No data returned from insert operation")
        except ValueError as ve: # Catch specific validation errors
            self.handle_error(str(ve), ve) # handle_error will re-raise SupabaseRepositoryError
            raise # Ensure it re-raises the SupabaseRepositoryError from handle_error
        except Exception as e:
            self.handle_error(f"Error creating business context for user {user_id}", e)
            raise # Re-raise

    def update_business_context(self, context_id, user_id, update_data):
        """Update an existing business context synchronously."""
        try:
            existing = self.get_by_id(context_id, user_id)
            if not existing:
                raise ValueError(
                    f"Business context {context_id} not found or does not belong to user {user_id}"
                )
            logger.debug(
                f"Updating business context: {context_id} for user: {user_id} with data: {update_data}"
            )
            update_data["updated_at"] = datetime.now(timezone.utc).isoformat()
            if "user_id" in update_data:
                del update_data["user_id"]

            table = self._get_table()
            result = (
                table
                .update(update_data)
                .eq("id", context_id)
                .eq("user_id", user_id)
                .execute()
            )
            if result.data and len(result.data) > 0:
                return result.data[0]
            # If update returns no data but didn't error, could mean RLS or row not found by filter
            # Since we checked existence, this implies an issue or RLS preventing seeing the update.
            raise SupabaseRepositoryError(f"Failed to update business context {context_id}: No data returned from update")
        except ValueError as ve:
            self.handle_error(str(ve), ve)
            raise
        except Exception as e:
            self.handle_error(
                f"Error updating business context {context_id} for user {user_id}", e
            )
            raise # Re-raise

    def delete_business_context(self, context_id, user_id):
        """Delete a business context synchronously."""
        try:
            existing = self.get_by_id(context_id, user_id)
            if not existing:
                raise ValueError(
                    f"Business context {context_id} not found or does not belong to user {user_id}"
                )
            table = self._get_table()
            (
                table
                .delete()
                .eq("id", context_id)
                .eq("user_id", user_id)
                .execute()
            )
            # Supabase delete doesn't typically return the deleted row(s) in `data` by default
            # Success is indicated by no error. We can assume success if no exception.
            return True
        except ValueError as ve:
            self.handle_error(str(ve), ve)
            raise
        except Exception as e:
            self.handle_error(
                f"Error deleting business context {context_id} for user {user_id}", e
            )
            # Original code returned False, but handle_error raises. So, re-raise.
            raise # Re-raise

    def get_latest_business_context(self, user_id):
        """Get the latest business context for a user synchronously."""
        try:
            table = self._get_table()
            result = (
                table
                .select("*")
                .eq("user_id", user_id)
                .order("updated_at", desc=True) # Assuming updated_at for latest, or created_at
                .limit(1)
                .maybe_single() # Use maybe_single for convenience
                .execute()
            )
            # maybe_single() returns the single row dict or None if not found, directly in result.data
            return result.data if result.data else None
        except Exception as e:
            self.handle_error(f"Error getting latest business context for user {user_id}", e)
            raise # Re-raise
