"""
Sample implementation of ContentItemRepository for Supabase integration
"""

from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime, timezone


from app.repositories.supabase_repository import (
    SupabaseRepository,
    SupabaseRepositoryError,
)

logger = logging.getLogger(__name__)


class ContentItemRepository(SupabaseRepository):
    """Repository for content items in Supabase."""

    def __init__(self, supabase_client: Optional["Client"] = None):
        """Initialize the repository with the content_items table and optional Supabase client.

        Args:
            supabase_client: Injected Supabase client instance. If not provided, the base class
                will fall back to the legacy synchronous client getter (back-compat mode).
        """
        super().__init__(table_name="content_items", supabase_client=supabase_client)

    def get_by_id(
        self, item_id: Union[str, int], user_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get a specific content item by ID synchronously, ensuring it belongs to the user."""
        logger.debug(
            f"Attempting to get content item by ID from table '{self.table_name}' with ID: {item_id} for user: {user_id}"
        )
        try:
            table = self._get_table()
            query = table.select("*").eq("id", item_id).eq("user_id", user_id)
            logger.debug(
                f"Executing Supabase query: table='{self.table_name}', select='*', eq=('id', {item_id}), eq=('user_id', {user_id})"
            )
            response = query.execute()
            logger.debug(
                f"Raw response from Supabase for get_by_id({item_id}, {user_id}) in '{self.table_name}': {response}"
            )
            if response.data:
                logger.debug(
                    f"Content item found for ID {item_id} and user {user_id}. Data: {response.data[0]}"
                )
                return response.data[0]
            else:
                logger.warning(
                    f"No content item found for ID {item_id} belonging to user {user_id}."
                )
                return None
        except Exception as e:
            logger.error(
                f"Exception occurred during get_by_id for item ID {item_id}, user {user_id} in table '{self.table_name}': {type(e).__name__} - {e}"
            )
            self.handle_error(
                f"Failed to get content item by ID {item_id} for user {user_id}", e
            )
            raise # Re-raise the exception from handle_error

    def get_by_user_id(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all content items for a specific user synchronously."""
        try:
            return self.get_all({"user_id": user_id}) # Call to sync base method
        except Exception as e:
            logger.error("Failed to get content items for user %s: %s", user_id, e)
            # self.get_all already calls handle_error, so just re-raise if it gets here
            # Or, if self.handle_error is preferred to be called at this level:
            # self.handle_error(f"Failed to get content items for user {user_id}", e)
            raise SupabaseRepositoryError(f"Failed to get content items for user {user_id}: {e}")

    def search(
        self, search_term: str, user_id: str, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Search content items using full-text search synchronously."""
        try:
            # PostgreSQL specific FTS. Ensure content_tsv column and GIN index exist.
            # The query string needs careful construction to prevent SQL injection if search_term is directly embedded.
            # Using parameterized queries or ensuring search_term is sanitized is crucial.
            # Supabase client `text_search` method is safer if applicable.
            # For now, assuming the existing string construction is acceptable with prior sanitization (not shown).

            # If sticking to raw filter, ensure search_term is escaped.
            # A safer approach with PostgREST textSearch: table.text_search('fts_column', f"'{search_term}'").eq('user_id', user_id)
            # However, the original code uses a complex custom FTS query string directly in .filter().
            # This is generally risky. Let's try to adapt to text_search if possible, or keep the raw filter with a warning.

            # Assuming 'content_tsv' is the tsvector column
            # and search_term is already sanitized or safe.
            table = self._get_table()
            # The .filter() method might not be ideal for complex FTS strings.
            # .rpc() for a stored procedure or more direct .text_search() might be better.
            # For now, let's assume text_search method can be used, if not, the raw filter approach is kept with its risks.
            response = (
                table.select("*")
                .text_search("content_tsv", f"'{search_term}'", config="english", ts_type="plainto") # Using plainto for safety
                .eq("user_id", user_id)
                # .order("rank", desc=True) # Assuming rank is available from text_search or a custom column
                .limit(limit)
                .execute()
            )
            return response.data
        except Exception as e:
            logger.error("Failed to search content items: %s", e)
            raise SupabaseRepositoryError(f"Failed to search content items for term '{search_term}': {e}")

    def get_by_type(self, content_type: str, user_id: str) -> List[Dict[str, Any]]:
        """Get content items by type for a specific user synchronously."""
        try:
            # Use the sync query method from the base class
            return self.query(
                lambda q: q.select("*")
                .eq("user_id", user_id)
                .eq("content_type", content_type)
                .order("created_at", desc=True)
            )
        except Exception as e:
            logger.error("Failed to get content items by type %s: %s", content_type, e)
            raise SupabaseRepositoryError(
                f"Failed to get content items by type {content_type} for user {user_id}: {e}"
            )

    def get_by_tags(self, tags: List[str], user_id: str) -> List[Dict[str, Any]]:
        """Get content items by tags for a specific user synchronously."""
        try:
            # Supabase/PostgREST supports array operations like 'cs' (contains) or 'ov' (overlaps)
            # which are safer and more efficient than string LIKE operations for array columns.
            # Assuming 'tags' column is of type array (e.g., text[]).
            if not tags: # Handle empty tags list
                return []
            table = self._get_table()
            response = (
                table.select("*")
                .eq("user_id", user_id)
                .cs("tags", tags)  # Use 'cs' if all tags must be present in some order, or 'ov' for any overlap
                .execute()
            )
            return response.data
        except Exception as e:
            logger.error("Failed to get content items by tags %s: %s", tags, e)
            raise SupabaseRepositoryError(
                f"Failed to get content items by tags {tags} for user {user_id}: {e}"
            )

    def get_filtered_items(
        self,
        user_id: str,
        search_term: Optional[str] = None,
        content_type: Optional[str] = None,
        tags: Optional[List[str]] = None,
        limit: int = 50,
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """Get content items with combined filters for a specific user synchronously."""
        try:
            logger.debug(
                f"Getting filtered items for user {user_id} with filters: "
                f"search='{search_term}', type='{content_type}', tags={tags}, limit={limit}, "
                f"sort_by='{sort_by}', sort_order='{sort_order}'"
            )
            table = self._get_table()
            query = table.select("*", count="exact").eq("user_id", user_id)

            if content_type:
                query = query.eq("content_type", content_type)
            if tags and len(tags) > 0:
                query = query.cs("tags", tags)

            # FTS and Sorting logic
            # If search_term is present, FTS rank might be preferred for sorting unless overridden.
            # The original logic for sorting with FTS was a bit complex.
            # Simplified: If search_term, and sort_by is not 'relevance' (or similar), apply sort_by.
            # Otherwise, if search_term, FTS will imply some ranking.
            # If no search_term, apply sort_by or default.

            if search_term:
                query = query.text_search("content_tsv", f"'{search_term}'", config="english", ts_type="plainto")
                # If a specific sort other than relevance is given, apply it. Otherwise, rely on FTS ranking.
                if sort_by and sort_order and sort_by != "relevance":
                    query = query.order(sort_by, desc=(sort_order == "desc"))
                # else: implicitly sort by FTS rank (if supabase client supports it or DB does)
            elif sort_by and sort_order:
                query = query.order(sort_by, desc=(sort_order == "desc"))
            else:
                query = query.order("updated_at", desc=True) # Default sort

            query = query.limit(limit)
            logger.debug(f"Executing combined filter query (final): {query}") # Logging the query object might not be fully representative
            response = query.execute()
            logger.debug(f"Raw response from Supabase for get_filtered_items: {response}")
            return response.data if response.data else []
        except Exception as e:
            logger.error(
                f"Failed to get filtered content items for user {user_id}: {type(e).__name__} - {e}"
            )
            self.handle_error(f"Failed to get filtered content items for user {user_id}", e)
            raise # Re-raise

    def create_content_item(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new content item synchronously. user_id must be in data."""
        if "user_id" not in data:
            raise ValueError("user_id is required to create a content item")
        data["created_at"] = datetime.now(timezone.utc).isoformat()
        data["updated_at"] = data["created_at"]
        # Use the base class create method which is now sync
        return super().create(data)

    def update_content_item(
        self, item_id: Union[str, int], user_id: str, data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Update a content item synchronously, ensuring it belongs to the user."""
        # Ensure user_id is not changed via this method
        if "user_id" in data and data["user_id"] != user_id:
            raise ValueError("Cannot change user_id of content item.")

        # Verify item belongs to user before update
        existing_item = self.get_by_id(item_id, user_id)
        if not existing_item:
            logger.warning(f"Content item {item_id} not found for user {user_id} during update attempt.")
            return None # Or raise NotFoundException

        data["updated_at"] = datetime.now(timezone.utc).isoformat()
        try:
            table = self._get_table()
            response = (
                table.update(data)
                .eq("id", item_id)
                .eq("user_id", user_id) # Redundant if get_by_id check passed, but safe
                .execute()
            )
            if response.data:
                return response.data[0]
            # If update returns no data but did not error, it implies row wasn't matched by filters
            # which should have been caught by get_by_id unless a race condition.
            logger.warning(f"Update for content item {item_id} returned no data, though item was verified.")
            return None
        except Exception as e:
            self.handle_error(f"Failed to update content item {item_id}", e)
            raise # Re-raise

    def delete_content_item(self, item_id: Union[str, int], user_id: str) -> bool:
        """Delete a content item synchronously, ensuring it belongs to the user."""
        # Verify item belongs to user before delete
        existing_item = self.get_by_id(item_id, user_id)
        if not existing_item:
            logger.warning(f"Content item {item_id} not found for user {user_id} during delete attempt.")
            return False # Or raise NotFoundException

        # Use the base class delete method which is now sync
        return super().delete(item_id) # Base delete only takes id
        # If user_id check is crucial for delete query itself (RLS might handle this):
        # try:
        #     table = await self._get_table()
        #     await table.delete().eq("id", item_id).eq("user_id", user_id).execute()
        #     return True
        # except Exception as e:
        #     self.handle_error(f"Failed to delete content item {item_id}", e)
        #     raise

    def get_recent_content_items(
        self, user_id: str, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get recent content items for a user synchronously."""
        try:
            table = self._get_table()
            response = (
                table.select("*")
                .eq("user_id", user_id)
                .order("updated_at", desc=True)
                .limit(limit)
                .execute()
            )
            return response.data if response.data else []
        except Exception as e:
            self.handle_error(f"Failed to get recent content items for user {user_id}", e)
            raise # Re-raise

    def get_business_context_items(
        self, user_id: str, business_context_id: str, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get content items linked to a specific business_context_id for a user synchronously."""
        try:
            table = self._get_table()
            response = (
                table.select("*")
                .eq("user_id", user_id)
                .eq("business_context_id", business_context_id)
                .order("updated_at", desc=True)
                .limit(limit)
                .execute()
            )
            return response.data if response.data else []
        except Exception as e:
            self.handle_error(f"Failed to get content items for business_context_id {business_context_id}", e)
            raise # Re-raise

    def get_related_content_items(
        self, user_id: str, content_item_id: str, limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Placeholder for getting related content items (e.g., by tags, type, or embeddings)."""
        # This is a simplified example. Real implementation would involve more complex logic.
        try:
            current_item = self.get_by_id(content_item_id, user_id)
            if not current_item or not current_item.get("tags"):
                return []

            tags_to_search = current_item.get("tags", [])
            if not tags_to_search:
                return []

            table = self._get_table()
            response = (
                table.select("*")
                .eq("user_id", user_id)
                .ov("tags", tags_to_search) # Overlaps with any of the tags
                .neq("id", content_item_id) # Exclude the item itself
                .limit(limit)
                .execute()
            )
            return response.data if response.data else []
        except Exception as e:
            self.handle_error(f"Failed to get related content items for {content_item_id}", e)
            raise # Re-raise
