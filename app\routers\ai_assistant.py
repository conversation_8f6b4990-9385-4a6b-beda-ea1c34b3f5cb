"""
AI Assistant routes for FastAPI.
Migrated from Flask app/ai_assistant blueprint to use FastAPI dependency injection.
This handles general AI assistant endpoints not covered by the chat router.
"""

import logging
from typing import Dict, Any, Optional, List, Annotated

from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field

from app.auth.dependencies import CurrentUserType, CurrentUserIdType, CurrentUser, get_current_user
from app.dependencies import get_business_context_repository, get_content_item_repository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ai_assistant", tags=["ai_assistant"])


# Pydantic models for request/response
class ContentGenerationRequest(BaseModel):
    prompt: str = Field(..., min_length=1, description="Content generation prompt")
    business_context_id: Optional[str] = Field(None, description="Business context ID to use")
    max_tokens: Optional[int] = Field(None, ge=1, le=100000, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0, description="Generation temperature")


class GenerateContentRequest(BaseModel):
    prompt: str
    business_context_id: Optional[str] = None
    content_example_ids: List[str] = []


class FormatTranscriptRequest(BaseModel):
    transcript: str
    options: dict = {}


class ContentGenerationResponse(BaseModel):
    message: str
    result: str
    tokens_used: Optional[int] = None


class AuthTestResponse(BaseModel):
    message: str
    user_id: str
    user_info: Dict[str, Any]
    token_snippet: str


# Migrated routes from configuration.py
@router.get("/test-connection")
async def test_connection(current_user: Annotated[CurrentUser, Depends(get_current_user)]):
    """
    Test the connection to the AI service

    This endpoint verifies that the application can successfully
    connect to the AI service and generate responses.

    Returns:
        dict: Status of the connection test
    """
    try:
        # Get AI generator
        from app.ai_assistant.common import get_ai_generator
        ai_generator = get_ai_generator()

        # Test connection
        test_result = ai_generator.test_connection()

        if test_result.get("success", False):
            return {
                "success": True,
                "message": "Successfully connected to AI service",
                "model": test_result.get("model", "unknown"),
                "provider": test_result.get("provider", "unknown"),
            }
        else:
            return {
                "success": False,
                "message": test_result.get(
                    "error_message", "Failed to connect to AI service"
                ),
                "error_details": test_result.get("error_details", ""),
            }

    except Exception as e:
        # Log the error
        logger.error("Error testing AI connection: %s", str(e))

        # Return error response
        return {
            "success": False,
            "message": "Failed to connect to AI service",
            "error_details": str(e),
        }


@router.get("/config")
async def get_ai_config_new(current_user: Annotated[CurrentUser, Depends(get_current_user)]):
    """
    Get the current AI assistant configuration

    This endpoint returns the current configuration for the AI assistant,
    including the model being used and available features.

    Returns:
        dict: Current configuration settings
    """
    try:
        # Get AI generator
        from app.ai_assistant.common import get_ai_generator
        ai_generator = get_ai_generator()

        # Get configuration
        config = {
            "model": ai_generator.model,
            "provider": ai_generator.provider,
            "features": {
                "chat": True,
                "content_generation": True,
                "youtube_analysis": hasattr(ai_generator, "youtube_service")
                and ai_generator.youtube_service is not None,
                "transcript_processing": hasattr(ai_generator, "youtube_service")
                and ai_generator.youtube_service is not None,
            },
            "max_context_length": ai_generator.max_context_length,
            "max_response_tokens": ai_generator.max_tokens,
        }

        return config

    except Exception as e:
        # Log the error
        logger.error("Error retrieving AI configuration: %s", str(e))

        # Return error response
        return {
            "error": True,
            "error_message": f"Failed to retrieve configuration: {str(e)}",
        }


# Migrated routes from content_generation.py  
@router.post("/generate-content")
async def generate_content_new(
    request_data: GenerateContentRequest,
    current_user: Annotated[CurrentUser, Depends(get_current_user)],
    business_context_repo = Depends(get_business_context_repository),
    content_item_repo = Depends(get_content_item_repository)
):
    """Generate content using AI based on prompt and optional context"""
    logger.debug("Entered /generate-content route function definition")
    try:
        logger.debug("Entered /generate-content route try block")

        # Get AI generator
        from app.ai_assistant.common import get_ai_generator
        ai_generator = get_ai_generator()
        logger.debug(f"Got AI generator: {ai_generator}")

        # Get current user ID
        current_user_id = current_user.user_id
        logger.debug(f"Got current_user_id: {current_user_id}")

        # Parse request data
        logger.debug(f"Got request data: {request_data}")

        prompt = request_data.prompt
        business_context_id = request_data.business_context_id
        content_example_ids = request_data.content_example_ids

        # Get business context if provided
        logger.debug("Checking for business_context_id")
        business_context = None
        if business_context_id:
            logger.debug("Attempting to get business context from repository")
            context = await business_context_repo.get_by_id(
                business_context_id, current_user_id
            )
            if context:
                business_context = {
                    "offer_description": context["offer_description"],
                    "target_audience": context["target_audience"],
                    "brand_voice": context["brand_voice"],
                    "key_benefits": context["key_benefits"],
                    "unique_value_proposition": context["unique_value_proposition"],
                }

        logger.debug("Checking for content_example_ids")

        # Get content examples if provided
        content_examples = []
        if content_example_ids:
            for example_id in content_example_ids:
                logger.debug("Attempting to get content example from repository")
                example = await content_item_repo.get_by_id(
                    example_id, current_user_id
                )
                if example:
                    content_examples.append(
                        {
                            "id": example["id"],
                            "title": example["title"],
                            "content": example["content"],
                            "tags": example["tags"],
                        }
                    )
        logger.debug("Calling ai_generator.generate_content")

        # Generate content using AI
        result = ai_generator.generate_content(
            prompt=prompt,
            business_context=business_context,
            content_examples=content_examples,
        )

        # Return the generated content
        return {
            "generated_content": result.get("generated_text", ""),
            "error": result.get("error", False),
            "error_message": result.get("error_message", None),
            "context_used": result.get("context_used", False),
            "examples_used": result.get("examples_used", False),
            "model": result.get("model", "unknown"),
        }

    except Exception as e:
        # Log the error
        logger.error("Error generating content: %s", str(e))

        # Return error response
        raise HTTPException(
            status_code=500,
            detail={
                "error": True,
                "error_message": str(e),
                "generated_content": "Error generating content. Please try again.",
            }
        )


@router.post("/format-transcript")
async def format_transcript(
    request_data: FormatTranscriptRequest,
    current_user: Annotated[CurrentUser, Depends(get_current_user)]
):
    """Format a transcript using AI"""
    try:
        # Get AI generator
        from app.ai_assistant.common import get_ai_generator
        ai_generator = get_ai_generator()

        # Parse request data
        transcript = request_data.transcript
        options = request_data.options

        # Validate request data
        if not transcript:
            raise HTTPException(
                status_code=400,
                detail={"success": False, "error": "No transcript provided"}
            )

        # Format the transcript
        format_type = options.get("format", "blog")
        tone = options.get("tone", "professional")
        length = options.get("length", "medium")
        keywords = options.get("keywords", [])

        result = ai_generator.format_transcript(
            transcript=transcript,
            format_type=format_type,
            tone=tone,
            length=length,
            keywords=keywords,
        )

        # Return the formatted transcript
        if "success" in result and result["success"]:
            return {
                "success": True, 
                "formatted_content": result.get("content")
            }
        else:
            raise HTTPException(
                status_code=500,
                detail={
                    "success": False,
                    "error": result.get("error", "Failed to format transcript"),
                }
            )

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        # Log the error
        logger.error("Error formatting transcript: %s", str(e))

        # Return error response
        raise HTTPException(
            status_code=500,
            detail={"success": False, "error": f"Error formatting transcript: {str(e)}"}
        )


@router.post("/generate", response_model=ContentGenerationResponse)
async def generate_content(
    generation_request: ContentGenerationRequest,
    current_user_id: CurrentUserIdType
) -> ContentGenerationResponse:
    """Generate content using AI with optional business context"""
    try:
        from app.ai_assistant.common import get_ai_generator, get_combined_business_context

        # Get AI generator
        ai_generator = get_ai_generator()

        # Get business context if provided
        business_context = None
        if generation_request.business_context_id:
            try:
                from app.dependencies import get_business_context_repository
                from app.models.supabase_client import get_supabase_client
                from app.repositories.supabase_business_context_repository import BusinessContextRepository
                
                supabase = await get_supabase_client()
                business_repo = BusinessContextRepository(supabase_client=supabase)
                business_context = await business_repo.get_by_id(
                    generation_request.business_context_id, current_user_id
                )
                
                if not business_context:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Business context not found"
                    )
            except Exception as e:
                logger.warning(f"Failed to get business context: {e}")
                # Continue without business context

        # Generate content using AI
        generation_params = {}
        if generation_request.max_tokens:
            generation_params["max_tokens"] = generation_request.max_tokens
        if generation_request.temperature:
            generation_params["temperature"] = generation_request.temperature

        result = await ai_generator.generate_content(
            prompt=generation_request.prompt,
            business_context=business_context,
            **generation_params
        )

        return ContentGenerationResponse(
            message="Content generated successfully",
            result=result,
            tokens_used=None  # Could be extracted from AI response if available
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error generating content: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Content generation failed: {str(e)}"
        )


@router.get("/auth-test", response_model=AuthTestResponse)
async def test_auth_token(current_user: CurrentUserType) -> AuthTestResponse:
    """Test endpoint to verify authentication and user info"""
    # Extract token snippet for debugging (safely)
    token_snippet = "No token available"
    if hasattr(current_user, 'token') and current_user.token:
        token = current_user.token
        if isinstance(token, str) and len(token) > 20:
            token_snippet = token[:10] + "..." + token[-10:]
        else:
            token_snippet = str(token)

    return AuthTestResponse(
        message="Authentication successful",
        user_id=current_user.user_id,
        user_info=current_user.user_data or {},
        token_snippet=token_snippet
    )


@router.get("/models", response_model=Dict[str, Any])
async def get_available_models() -> Dict[str, Any]:
    """Get available AI models and their configurations"""
    try:
        from app.ai_assistant.config import (
            get_model_for_purpose,
            get_api_provider,
            get_max_tokens
        )

        models_info = {
            "provider": get_api_provider(),
            "models": {
                "default": get_model_for_purpose("default"),
                "general_chat": get_model_for_purpose("general_chat"),
                "chat_stream": get_model_for_purpose("chat_stream"),
                "content_generation": get_model_for_purpose("content_generation"),
            },
            "max_tokens": {
                "default": get_max_tokens("default"),
                "chat": get_max_tokens("chat"),
                "chat_stream": get_max_tokens("chat_stream"),
                "content_generation": get_max_tokens("content_generation"),
            },
        }

        return {
            "message": "Available models retrieved successfully",
            "models": models_info
        }

    except Exception as e:
        logger.error("Error retrieving model information: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve model information"
        )


@router.get("/health", response_model=Dict[str, Any])
async def ai_health_check() -> Dict[str, Any]:
    """Health check for AI assistant services"""
    try:
        from app.ai_assistant.config import (
            get_api_provider,
            get_api_key,
            get_api_url,
            get_model_for_purpose
        )

        # Check AI configuration
        provider = get_api_provider()
        api_key_present = bool(get_api_key())
        api_url = get_api_url()
        default_model = get_model_for_purpose("default")

        # Test AI generator initialization
        ai_generator_status = "unknown"
        try:
            from app.ai_assistant.common import get_ai_generator
            ai_generator = get_ai_generator()
            ai_generator_status = "initialized" if ai_generator else "failed"
        except Exception as e:
            ai_generator_status = f"error: {str(e)}"

        return {
            "status": "healthy",
            "message": "AI Assistant services are operational",
            "provider": provider,
            "api_key_present": api_key_present,
            "api_url": api_url,
            "default_model": default_model,
            "ai_generator_status": ai_generator_status,
        }

    except Exception as e:
        logger.error("Error in AI health check: %s", str(e))
        return {
            "status": "unhealthy",
            "message": f"AI Assistant services are experiencing issues: {str(e)}",
            "error": str(e)
        }


@router.post("/test-generation", response_model=Dict[str, Any])
async def test_generation(
    current_user_id: CurrentUserIdType
) -> Dict[str, Any]:
    """Test AI generation with a simple prompt"""
    try:
        from app.ai_assistant.common import get_ai_generator

        # Get AI generator
        ai_generator = get_ai_generator()

        # Test with a simple prompt
        test_prompt = "Say 'Hello, this is a test of the AI generation system.'"
        
        result = await ai_generator.generate_content(
            prompt=test_prompt,
            max_tokens=50
        )

        return {
            "message": "AI generation test successful",
            "test_prompt": test_prompt,
            "result": result,
            "user_id": current_user_id
        }

    except Exception as e:
        logger.error("Error in AI generation test: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI generation test failed: {str(e)}"
        )


@router.get("/config", response_model=Dict[str, Any])
async def get_ai_config() -> Dict[str, Any]:
    """Get current AI configuration for debugging"""
    try:
        from app.ai_assistant.config import (
            get_model_for_purpose,
            get_api_provider,
            get_api_key,
            get_api_url,
            get_max_tokens,
        )

        config_info = {
            "provider": get_api_provider(),
            "api_key_present": bool(get_api_key()),
            "api_key_length": len(get_api_key()) if get_api_key() else 0,
            "api_url": get_api_url(),
            "models": {
                "default": get_model_for_purpose("default"),
                "general_chat": get_model_for_purpose("general_chat"),
                "chat_stream": get_model_for_purpose("chat_stream"),
            },
            "max_tokens": {
                "default": get_max_tokens("default"),
                "chat": get_max_tokens("chat"),
                "chat_stream": get_max_tokens("chat_stream"),
            },
        }

        return {
            "message": "AI configuration retrieved successfully",
            "config": config_info
        }

    except Exception as e:
        logger.error("Error retrieving AI configuration: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve AI configuration"
        )
